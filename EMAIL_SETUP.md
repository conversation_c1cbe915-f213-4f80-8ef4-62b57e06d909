# E-posta Sistemi Kurulumu ve Kullanımı

## Hız<PERSON><PERSON>şlangıç (Test Modu)

E-posta gönderme özelliğini test etmek için aşağıdaki adımları takip edin:

### 1. Test E-posta Konfigürasyonu Oluşturma

```bash
# Backend dizininde
cd backend
python setup_test_email.py
```

Veya Django management komutu ile:

```bash
cd backend
python manage.py setup_test_email
```

### 2. Test Modu Nasıl Çalışır?

- **DEBUG=True** olduğunda sistem otomatik olarak test moduna geçer
- E-postalar gerçekten gönderilmez
- Bunun yerine `backend/test_emails/` klasörüne JSON dosyası olarak kaydedilir
- Bu sayede e-posta içeriğini ve alıcıları güvenle kontrol edebilirsiniz

### 3. Test E-postalarını Görüntüleme

#### API Endpoint'leri:
```
GET /api/v1/communications/messages/test_emails/
GET /api/v1/communications/messages/email-status/
```

#### Test e-posta dosyaları:
```
backend/test_emails/email_YYYYMMDD_HHMMSS_microseconds.json
```

## E-posta Gönderme Süreci

### 1. Frontend'den E-posta Gönderme

```javascript
// Compose sayfasından e-posta gönderme
const emailData = {
  subject: "Test Konusu",
  content: "Test içeriği",
  recipients: [{ email: "<EMAIL>" }],
  company_id: 1,
  cc: [{ email: "<EMAIL>" }],
  bcc: [{ email: "<EMAIL>" }]
};

await sendEmail(emailData);
```

### 2. Backend İşlem Süreci

1. **Doğrulama**: Alıcılar ve içerik kontrol edilir
2. **Konfigürasyon**: E-posta konfigürasyonu bulunur (test modunda atlanır)
3. **Kayıt**: E-posta veritabanına kaydedilir
4. **Gönderim**: 
   - Test modunda: JSON dosyasına kaydedilir
   - Production modunda: SendGrid ile gönderilir

## Hata Giderme

### 400 Bad Request Hatası

Bu hata genellikle şu durumlardan kaynaklanır:

1. **E-posta konfigürasyonu eksik**:
   ```bash
   python setup_test_email.py
   ```

2. **Geçersiz alıcı formatı**:
   ```javascript
   // Yanlış
   recipients: ["<EMAIL>"]
   
   // Doğru
   recipients: [{ email: "<EMAIL>" }]
   ```

3. **Eksik zorunlu alanlar**:
   - `subject` (konu)
   - `content` (içerik)
   - `recipients` (alıcılar)

### E-posta Durumunu Kontrol Etme

```bash
curl http://localhost:8000/api/v1/communications/messages/email-status/
```

Yanıt:
```json
{
  "test_mode": true,
  "active_configs": 1,
  "has_default_config": true,
  "ready_to_send": true,
  "message": "Test modu aktif - e-postalar test_emails klasörüne kaydedilecek"
}
```

## Production Kurulumu

Gerçek e-posta göndermek için:

1. **DEBUG=False** yapın
2. **SendGrid API Key** ekleyin
3. **Gerçek SMTP ayarları** yapılandırın
4. **E-posta konfigürasyonu** oluşturun

### SendGrid Konfigürasyonu

```python
# settings.py
SENDGRID_API_KEY = 'your-sendgrid-api-key'
```

### SMTP Konfigürasyonu

Django admin panelinden veya API ile EmailConfig oluşturun:

```python
EmailConfig.objects.create(
    name='Production SMTP',
    smtp_server='smtp.gmail.com',
    smtp_port=587,
    use_tls=True,
    email_address='<EMAIL>',
    display_name='Your Name',
    username='<EMAIL>',
    password='your-app-password',
    is_active=True,
    is_default=True
)
```

## API Endpoints

### E-posta Gönderme
- `POST /api/v1/communications/messages/send/`

### Taslak Kaydetme
- `POST /api/v1/communications/messages/drafts/`

### E-posta Listesi
- `GET /api/v1/communications/messages/`

### Test E-postaları (sadece DEBUG=True)
- `GET /api/v1/communications/messages/test_emails/`

### Sistem Durumu
- `GET /api/v1/communications/messages/email-status/`
