from django.shortcuts import render
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.parsers import <PERSON>PartParser, FormParser
from django.utils import timezone
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import os
import mimetypes

from .email_backend import TestEmailBackend, get_test_emails, get_test_email
from .sendgrid_service import sendgrid_service
from .models import EmailTemplate, EmailMessage, EmailConfig, EmailAttachment
from .serializers import (
    EmailTemplateSerializer,
    EmailConfigSerializer,
    EmailMessageListSerializer,
    EmailMessageDetailSerializer,
    SendEmailSerializer,
    EmailAttachmentSerializer
)
from customers.models import Company, Contact
from authentication.models import UserProfile


class EmailTemplateViewSet(viewsets.ModelViewSet):
    """
    E-posta şablonları için API endpoint'i
    """
    queryset = EmailTemplate.objects.all()
    serializer_class = EmailTemplateSerializer
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'subject', 'content']


class EmailConfigViewSet(viewsets.ModelViewSet):
    """
    E-posta konfigürasyonları için API endpoint'i
    """
    queryset = EmailConfig.objects.all()
    serializer_class = EmailConfigSerializer


class EmailMessageViewSet(viewsets.ModelViewSet):
    """
    E-posta mesajları için API endpoint'i
    """
    queryset = EmailMessage.objects.all().order_by('-created_at')
    filter_backends = [filters.SearchFilter]
    search_fields = ['subject', 'content', 'sender', 'company__name', 'contact__first_name', 'contact__last_name']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return EmailMessageListSerializer
        return EmailMessageDetailSerializer
    
    @action(detail=False, methods=['post'], url_path='send')
    def send_email(self, request):
        """
        SendGrid ile e-posta gönderme endpoint'i
        """
        serializer = SendEmailSerializer(data=request.data)
        
        if serializer.is_valid():
            data = serializer.validated_data
            
            # İlgili firma ve kişi nesnelerini bulalım
            company = None
            contact = None
            
            if data.get('company_id'):
                try:
                    company = Company.objects.get(id=data['company_id'])
                except Company.DoesNotExist:
                    return Response(
                        {"error": "Belirtilen firma bulunamadı."},
                        status=status.HTTP_404_NOT_FOUND
                    )
            
            if data.get('contact_id'):
                try:
                    contact = Contact.objects.get(id=data['contact_id'])
                except Contact.DoesNotExist:
                    return Response(
                        {"error": "Belirtilen kişi bulunamadı."},
                        status=status.HTTP_404_NOT_FOUND
                    )
            
            # E-posta yapılandırması bulalım (test modu için opsiyonel)
            config = None
            use_test_mode = settings.DEBUG  # Debug modunda test modu kullan

            if not use_test_mode:
                try:
                    if data.get('config_id'):
                        config = EmailConfig.objects.get(id=data['config_id'], is_active=True)
                    else:
                        # Varsayılan yapılandırmayı kullan
                        config = EmailConfig.objects.filter(is_default=True, is_active=True).first()

                        if not config:
                            # Varsayılan yoksa aktif bir yapılandırma bul
                            config = EmailConfig.objects.filter(is_active=True).first()

                    if not config:
                        return Response(
                            {"error": "Aktif bir e-posta yapılandırması bulunamadı."},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                except EmailConfig.DoesNotExist:
                    return Response(
                        {"error": "Belirtilen e-posta yapılandırması bulunamadı veya aktif değil."},
                        status=status.HTTP_404_NOT_FOUND
                    )
            
            # Kullanıcının e-posta ayarlarını al
            try:
                user_profile = UserProfile.objects.get(user=request.user)
                sender_email = user_profile.sender_email or request.user.email
                sender_name = user_profile.sender_name or f"{request.user.first_name} {request.user.last_name}".strip()
            except UserProfile.DoesNotExist:
                sender_email = request.user.email
                sender_name = f"{request.user.first_name} {request.user.last_name}".strip()

            if not sender_email:
                return Response(
                    {"error": "Gönderen e-posta adresi belirtilmemiş. Lütfen profil ayarlarınızı kontrol edin."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # E-posta nesnesi oluşturalım
            email = EmailMessage(
                subject=data['subject'],
                content=data['content'],
                sender=sender_email,
                recipients=data['recipients'],
                cc=data.get('cc'),
                bcc=data.get('bcc'),
                attachments=data.get('attachments'),
                status='sending',
                company=company,
                contact=contact,
                metadata={
                    'sent_by_user_id': request.user.id,
                    'sent_by_username': request.user.username,
                    'sender_name': sender_name,
                }
            )
            email.save()

            try:
                if use_test_mode:
                    # Test modu - gerçek e-posta gönderme
                    from .email_backend import TestEmailBackend

                    test_backend = TestEmailBackend()

                    # Test için basit mesaj formatı
                    test_message = {
                        "Subject": data['subject'],
                        "get_payload": lambda: data['content']
                    }

                    # Alıcıları basit liste formatına çevir
                    to_emails = []
                    for recipient in data['recipients']:
                        if isinstance(recipient, dict):
                            to_emails.append(recipient.get('email', ''))
                        else:
                            to_emails.append(str(recipient))

                    cc_emails = []
                    if data.get('cc'):
                        for recipient in data['cc']:
                            if isinstance(recipient, dict):
                                cc_emails.append(recipient.get('email', ''))
                            else:
                                cc_emails.append(str(recipient))

                    bcc_emails = []
                    if data.get('bcc'):
                        for recipient in data['bcc']:
                            if isinstance(recipient, dict):
                                bcc_emails.append(recipient.get('email', ''))
                            else:
                                bcc_emails.append(str(recipient))

                    success, message = test_backend.send_email(
                        from_email=sender_email,
                        to_emails=to_emails,
                        message=test_message,
                        cc=cc_emails,
                        bcc=bcc_emails
                    )
                    response_data = {"test_mode": True}

                else:
                    # Gerçek e-posta gönderimi
                    # Alıcıları formatla
                    to_emails = sendgrid_service.format_recipients(data['recipients'])
                    cc_emails = sendgrid_service.format_recipients(data.get('cc', [])) if data.get('cc') else None
                    bcc_emails = sendgrid_service.format_recipients(data.get('bcc', [])) if data.get('bcc') else None

                    # Ekleri hazırla
                    attachments = []
                    if data.get('attachments'):
                        for attachment_data in data['attachments']:
                            if isinstance(attachment_data, dict):
                                attachments.append(attachment_data)

                    # SendGrid ile e-posta gönder
                    success, message, response_data = sendgrid_service.send_email(
                        from_email=sender_email,
                        from_name=sender_name,
                        to_emails=to_emails,
                        subject=data['subject'],
                        content=data['content'],
                        cc_emails=cc_emails,
                        bcc_emails=bcc_emails,
                        attachments=attachments
                    )

                if success:
                    # Başarılı gönderim
                    email.status = 'sent'
                    email.sent_at = timezone.now()
                    email.metadata.update({
                        'response_data': response_data,
                        'test_mode': use_test_mode
                    })
                    email.save()

                    # Alıcı sayısını hesapla
                    total_recipients = len(data['recipients'])
                    if data.get('cc'):
                        total_recipients += len(data['cc'])
                    if data.get('bcc'):
                        total_recipients += len(data['bcc'])

                    response_message = message
                    if use_test_mode:
                        response_message = f"Test modu: E-posta kaydedildi. {message}"

                    return Response(
                        {
                            "message": response_message,
                            "email_id": email.id,
                            "recipients_count": total_recipients,
                            "test_mode": use_test_mode
                        },
                        status=status.HTTP_200_OK
                    )
                else:
                    # Gönderim hatası
                    email.status = 'failed'
                    email.error_message = message
                    email.save()

                    return Response(
                        {
                            "error": "E-posta gönderilirken bir hata oluştu.",
                            "details": message,
                            "email_id": email.id
                        },
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

            except Exception as e:
                # Beklenmeyen hata
                email.status = 'failed'
                email.error_message = str(e)
                email.save()

                return Response(
                    {
                        "error": "E-posta gönderilirken beklenmeyen bir hata oluştu.",
                        "details": str(e),
                        "email_id": email.id
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], url_path='drafts')
    def save_draft(self, request):
        """
        E-posta taslağı kaydetme endpoint'i
        """
        serializer = SendEmailSerializer(data=request.data)

        if serializer.is_valid():
            data = serializer.validated_data

            # İlgili firma ve kişi nesnelerini bulalım
            company = None
            contact = None

            if data.get('company_id'):
                try:
                    company = Company.objects.get(id=data['company_id'])
                except Company.DoesNotExist:
                    return Response(
                        {"error": "Belirtilen firma bulunamadı."},
                        status=status.HTTP_404_NOT_FOUND
                    )

            if data.get('contact_id'):
                try:
                    contact = Contact.objects.get(id=data['contact_id'])
                except Contact.DoesNotExist:
                    return Response(
                        {"error": "Belirtilen kişi bulunamadı."},
                        status=status.HTTP_404_NOT_FOUND
                    )

            # Kullanıcının e-posta ayarlarını al
            try:
                user_profile = UserProfile.objects.get(user=request.user)
                sender_email = user_profile.sender_email or request.user.email
                sender_name = user_profile.sender_name or f"{request.user.first_name} {request.user.last_name}".strip()
            except UserProfile.DoesNotExist:
                sender_email = request.user.email
                sender_name = f"{request.user.first_name} {request.user.last_name}".strip()

            if not sender_email:
                return Response(
                    {"error": "Gönderen e-posta adresi belirtilmemiş. Lütfen profil ayarlarınızı kontrol edin."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Taslak e-posta nesnesi oluşturalım
            email = EmailMessage(
                subject=data.get('subject', ''),
                content=data.get('content', ''),
                sender=sender_email,
                recipients=data.get('recipients', []),
                cc=data.get('cc'),
                bcc=data.get('bcc'),
                attachments=data.get('attachments'),
                status='draft',
                company=company,
                contact=contact,
                metadata={
                    'created_by_user_id': request.user.id,
                    'created_by_username': request.user.username,
                    'sender_name': sender_name,
                }
            )
            email.save()

            return Response(
                {
                    "message": "Taslak başarıyla kaydedildi.",
                    "email_id": email.id
                },
                status=status.HTTP_201_CREATED
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'], url_path='email-status')
    def email_status(self, request):
        """
        E-posta sistemi durumunu kontrol et
        """
        # Aktif konfigürasyon var mı?
        active_configs = EmailConfig.objects.filter(is_active=True).count()
        default_config = EmailConfig.objects.filter(is_default=True, is_active=True).first()

        # Test modu mu?
        test_mode = settings.DEBUG

        status_info = {
            "test_mode": test_mode,
            "active_configs": active_configs,
            "has_default_config": default_config is not None,
            "ready_to_send": test_mode or (active_configs > 0),
            "message": ""
        }

        if test_mode:
            status_info["message"] = "Test modu aktif - e-postalar test_emails klasörüne kaydedilecek"
        elif active_configs == 0:
            status_info["message"] = "Aktif e-posta konfigürasyonu bulunamadı"
        elif not default_config:
            status_info["message"] = "Varsayılan e-posta konfigürasyonu bulunamadı"
        else:
            status_info["message"] = "E-posta sistemi hazır"

        return Response(status_info)

    @action(detail=False, methods=['post'], url_path='upload-attachment', parser_classes=[MultiPartParser, FormParser])
    def upload_attachment(self, request):
        """
        E-posta eki yükleme endpoint'i
        """
        if 'file' not in request.FILES:
            return Response(
                {"error": "Dosya yüklenmedi."},
                status=status.HTTP_400_BAD_REQUEST
            )

        uploaded_file = request.FILES['file']

        # Dosya boyutu kontrolü (10MB limit)
        max_size = 10 * 1024 * 1024  # 10MB
        if uploaded_file.size > max_size:
            return Response(
                {"error": "Dosya boyutu 10MB'dan büyük olamaz."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Dosya tipini kontrol et
        allowed_types = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain',
            'image/jpeg',
            'image/png',
            'image/gif',
        ]

        content_type = uploaded_file.content_type
        if content_type not in allowed_types:
            return Response(
                {"error": f"Desteklenmeyen dosya tipi: {content_type}"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Dosyayı geçici olarak kaydet
            import uuid
            file_name = f"{uuid.uuid4()}_{uploaded_file.name}"
            file_path = f"temp_attachments/{file_name}"

            # Dosyayı kaydet
            saved_path = default_storage.save(file_path, uploaded_file)

            # Base64 encode et
            import base64
            with default_storage.open(saved_path, 'rb') as f:
                file_content = f.read()
                encoded_content = base64.b64encode(file_content).decode()

            # Geçici dosyayı sil
            default_storage.delete(saved_path)

            return Response({
                "file_name": uploaded_file.name,
                "file_content": encoded_content,
                "file_size": uploaded_file.size,
                "content_type": content_type
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": f"Dosya yüklenirken hata oluştu: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def company_emails(self, request):
        """
        Belirli bir firmaya ait e-postaları listeler
        """
        company_id = request.query_params.get('company_id')
        if not company_id:
            return Response({"error": "Firma ID'si belirtilmelidir"}, status=status.HTTP_400_BAD_REQUEST)
            
        emails = self.queryset.filter(company_id=company_id)
        page = self.paginate_queryset(emails)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
            
        serializer = self.get_serializer(emails, many=True)
        return Response(serializer.data)
    @action(detail=False, methods=['get'])
    def contact_emails(self, request):
        """
        Belirli bir kişiye ait e-postaları listeler
        """
        contact_id = request.query_params.get('contact_id')
        if not contact_id:
            return Response({"error": "Kişi ID'si belirtilmelidir"}, status=status.HTTP_400_BAD_REQUEST)
            
        emails = self.queryset.filter(contact_id=contact_id)
        page = self.paginate_queryset(emails)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
            
        serializer = self.get_serializer(emails, many=True)
        return Response(serializer.data)
        
    @action(detail=False, methods=['get'])
    def test_emails(self, request):
        """
        Geliştirme ortamında kaydedilen test e-postalarını listele
        Bu endpoint sadece DEBUG=True olduğunda çalışır
        """
        if not settings.DEBUG:
            return Response(
                {"error": "Bu endpoint sadece geliştirme ortamında kullanılabilir."},
                status=status.HTTP_403_FORBIDDEN
            )
            
        emails = get_test_emails()
        return Response(emails)
    
    @action(detail=True, methods=['get'], url_path='test-email')
    def test_email_detail(self, request, pk=None):
        """
        Belirli bir test e-postasının detaylarını görüntüle
        Bu endpoint sadece DEBUG=True olduğunda çalışır
        """
        if not settings.DEBUG:
            return Response(
                {"error": "Bu endpoint sadece geliştirme ortamında kullanılabilir."},
                status=status.HTTP_403_FORBIDDEN
            )
            
        email_data = get_test_email(pk)
        if not email_data:
            return Response(
                {"error": "E-posta bulunamadı."},
                status=status.HTTP_404_NOT_FOUND
            )
            
        return Response(email_data)
