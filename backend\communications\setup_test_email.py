from django.core.management.base import BaseCommand
from .models import EmailConfig


class Command(BaseCommand):
    help = 'Test e-posta konfigürasyonu oluşturur'

    def handle(self, *args, **options):
        # Mevcut test konfigürasyonunu kontrol et
        test_config = EmailConfig.objects.filter(name='Test Configuration').first()
        
        if test_config:
            self.stdout.write(
                self.style.WARNING('Test e-posta konfigürasyonu zaten mevcut.')
            )
            return
        
        # Test konfigürasyonu oluştur
        EmailConfig.objects.create(
            name='Test Configuration',
            smtp_server='localhost',
            smtp_port=587,
            use_tls=True,
            email_address='<EMAIL>',
            display_name='Test Sender',
            username='test',
            password='test',
            is_active=True,
            is_default=True
        )
        
        self.stdout.write(
            self.style.SUCCESS('Test e-posta konfigürasyonu ba<PERSON>ar<PERSON>yla oluşturuldu.')
        )
        self.stdout.write(
            'Debug modunda e-postalar gerçekten gönderilmez, sadece test_emails klasörüne kaydedilir.'
        )
